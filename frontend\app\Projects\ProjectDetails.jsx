import React, { useContext, useState } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    StatusBar,
    SafeAreaView,


    Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import BackButton from '../Components/Shared/BackButton';
import { useQuery } from '@tanstack/react-query';
import { fetchProjectDetails } from '../../api/projects/projectApi';
import Toast from 'react-native-toast-message';
import ProgressTracker from './components/ProgressTracker';
import ProjectChat from './components/ProjectChat';

export default function ProjectDetails() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();
    const { id } = useLocalSearchParams();


    const {
        data: projectData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['projectDetails', id],
        queryFn: () => fetchProjectDetails(id),
        enabled: !!id,
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to fetch project details',
            });
        },
    });

    const project = projectData?.project;
    const requirement = projectData?.requirement;

    // Merge project and requirement data for easier access
    const mergedProject = project && requirement ? {
        ...project,
        projectType: requirement.projectType,
        constructionType: requirement.constructionType,
        location: requirement.location,
        budget: requirement.budget,
        designPreferences: requirement.designPreferences,
        additionalFacilities: requirement.additionalFacilities,
        brokerAssistanceRequired: requirement.brokerAssistanceRequired,
        specialInstructions: requirement.specialInstructions,
        expectedStartDate: requirement.expectedStartDate,
        expectedCompletionDate: requirement.expectedCompletionDate,
    } : project;



    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget))
            return 'Not specified';

        const formatAmount = (amount) => {
            if (amount >= 10000000)
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
            if (amount >= 1000) return `₹${(amount / 1000).toFixed(1)}K`;
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        }
        if (budget.minBudget) return `From ${formatAmount(budget.minBudget)}`;
        if (budget.maxBudget) return `Up to ${formatAmount(budget.maxBudget)}`;
        return 'Not specified';
    };

    const handleHireBroker = () => {
        if (!mergedProject?.brokerAssistanceRequired) {
            Alert.alert(
                'Broker Not Required',
                'This project does not require broker assistance.',
                [{ text: 'OK' }]
            );
            return;
        }
        router.push(`/Projects/HireProfessionals?projectId=${id}&type=broker`);
    };

    const handleHireContractor = () => {
        if (mergedProject?.brokerAssistanceRequired && !mergedProject?.brokerId) {
            Alert.alert(
                'Broker Required First',
                'You need to hire a broker before hiring a contractor for this project.',
                [{ text: 'OK' }]
            );
            return;
        }
        router.push(
            `/Projects/HireProfessionals?projectId=${id}&type=contractor`
        );
    };

    const InfoSection = ({ title, icon, children }) => (
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
            <View style={styles.sectionHeader}>
                <MaterialIcons name={icon} size={20} color={theme.PRIMARY} />
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    {title}
                </Text>
            </View>
            <View style={styles.sectionContent}>{children}</View>
        </View>
    );

    const InfoRow = ({ label, value, icon }) => (
        <View style={styles.infoRow}>
            {icon && (
                <MaterialIcons
                    name={icon}
                    size={16}
                    color={theme.TEXT_SECONDARY}
                />
            )}
            <View style={styles.infoContent}>
                <Text
                    style={[styles.infoLabel, { color: theme.TEXT_SECONDARY }]}
                >
                    {label}
                </Text>
                <Text style={[styles.infoValue, { color: theme.TEXT_PRIMARY }]}>
                    {value || 'Not specified'}
                </Text>
            </View>
        </View>
    );

    if (isLoading) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                <View style={[styles.header, { backgroundColor: theme.CARD }]}>
                    <BackButton
                        color={theme.TEXT_PRIMARY}
                        onPress={() => router.back()}
                    />
                    <Text
                        style={[
                            styles.headerTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Project Details
                    </Text>
                    <View style={{ width: 24 }} />
                </View>
                <View style={styles.loadingContainer}>
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Loading project details...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (!mergedProject) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                <View style={[styles.header, { backgroundColor: theme.CARD }]}>
                    <BackButton
                        color={theme.TEXT_PRIMARY}
                        onPress={() => router.back()}
                    />
                    <Text
                        style={[
                            styles.headerTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Project Details
                    </Text>
                    <View style={{ width: 24 }} />
                </View>
                <View style={styles.errorContainer}>
                    <MaterialIcons
                        name="error-outline"
                        size={60}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.errorText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Project not found
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Header */}
            <View style={[styles.header, { backgroundColor: theme.CARD }]}>
                <BackButton
                    color={theme.TEXT_PRIMARY}
                    onPress={() => router.back()}
                />
                <Text
                    style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}
                    numberOfLines={1}
                >
                    {mergedProject.projectName}
                </Text>
                {/* Only show edit button for project owners */}
                {mergedProject.userRoleInProject === 'owner' || mergedProject.userId === user?.id ? (
                    <TouchableOpacity
                        onPress={() =>
                            router.push(`/Projects/EditProject?id=${id}`)
                        }
                        style={styles.editButton}
                    >
                        <MaterialIcons
                            name="edit"
                            size={20}
                            color={theme.TEXT_SECONDARY}
                        />
                    </TouchableOpacity>
                ) : null}
            </View>

            <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}

            >
                <View style={styles.content}>
                    {/* Project Overview */}
                    <InfoSection title="Project Overview" icon="assignment">
                        <InfoRow
                            label="Project Type"
                            value={mergedProject.projectType || 'Not specified'}
                        />
                        <InfoRow
                            label="Construction Type"
                            value={mergedProject.constructionType || 'Not specified'}
                        />
                        <InfoRow label="Status" value="Active" />
                        <InfoRow
                            label="Created"
                            value={new Date(
                                mergedProject.createdAt
                            ).toLocaleDateString()}
                        />
                    </InfoSection>

                    {/* Location Details */}
                    <InfoSection title="Location" icon="location-on">
                        <InfoRow
                            label="Address"
                            value={mergedProject.location?.address || 'Not specified'}
                        />
                        <InfoRow label="City" value={mergedProject.location?.city || 'Not specified'} />
                        <InfoRow
                            label="State"
                            value={mergedProject.location?.state || 'Not specified'}
                        />
                        <InfoRow
                            label="Pincode"
                            value={mergedProject.location?.pincode || 'Not specified'}
                        />
                        {mergedProject.location?.plotSizeSqFt && (
                            <InfoRow
                                label="Plot Size"
                                value={`${mergedProject.location.plotSizeSqFt} sq ft`}
                            />
                        )}
                    </InfoSection>

                    {/* Budget Information */}
                    <InfoSection title="Budget" icon="attach-money">
                        <InfoRow
                            label="Budget Range"
                            value={formatBudget(mergedProject.budget)}
                        />
                    </InfoSection>

                    {/* Design Preferences */}
                    {mergedProject.designPreferences && (
                        <InfoSection title="Design Preferences" icon="home">
                            {mergedProject.designPreferences.floors && (
                                <InfoRow
                                    label="Floors"
                                    value={mergedProject.designPreferences.floors.toString()}
                                />
                            )}
                            {mergedProject.designPreferences.bedrooms && (
                                <InfoRow
                                    label="Bedrooms"
                                    value={mergedProject.designPreferences.bedrooms.toString()}
                                />
                            )}
                            {mergedProject.designPreferences.bathrooms && (
                                <InfoRow
                                    label="Bathrooms"
                                    value={mergedProject.designPreferences.bathrooms.toString()}
                                />
                            )}
                            <InfoRow
                                label="Parking Required"
                                value={
                                    mergedProject.designPreferences.parkingRequired
                                        ? 'Yes'
                                        : 'No'
                                }
                            />
                            <InfoRow
                                label="Garden Required"
                                value={
                                    mergedProject.designPreferences.gardenRequired
                                        ? 'Yes'
                                        : 'No'
                                }
                            />
                            <InfoRow
                                label="Vastu Compliance"
                                value={
                                    mergedProject.designPreferences.vastuCompliance
                                        ? 'Yes'
                                        : 'No'
                                }
                            />
                        </InfoSection>
                    )}

                    {/* Additional Details */}
                    {(mergedProject.specialInstructions ||
                        mergedProject.additionalFacilities?.length > 0) && (
                            <InfoSection
                                title="Additional Details"
                                icon="description"
                            >
                                {mergedProject.additionalFacilities?.length > 0 && (
                                    <InfoRow
                                        label="Additional Facilities"
                                        value={mergedProject.additionalFacilities.join(
                                            ', '
                                        )}
                                    />
                                )}
                                {mergedProject.specialInstructions && (
                                    <InfoRow
                                        label="Special Instructions"
                                        value={mergedProject.specialInstructions}
                                    />
                                )}
                            </InfoSection>
                        )}

                    {/* Hired Professionals */}
                    <InfoSection title="Team" icon="group">
                        <InfoRow
                            label="Broker Assistance"
                            value={
                                mergedProject.brokerAssistanceRequired
                                    ? 'Required'
                                    : 'Not Required'
                            }
                        />
                        <InfoRow
                            label="Broker Status"
                            value={mergedProject.brokerId ? 'Hired' : 'Not Hired'}
                        />
                        <InfoRow
                            label="Contractor Status"
                            value={mergedProject.contractorId ? 'Hired' : 'Not Hired'}
                        />
                    </InfoSection>

                    {/* Progress Tracking - Show for all project participants */}
                    <ProgressTracker projectId={id} project={mergedProject} />

                    {/* Project Communication - Show if contractor or broker is assigned */}
                    {(mergedProject.contractorId || mergedProject.brokerId) && (
                        <ProjectChat projectId={id} project={mergedProject} />
                    )}

                    {/* Action Buttons */}
                    <View style={styles.actionsContainer}>
                        {mergedProject.brokerAssistanceRequired &&
                            !mergedProject.brokerId && (
                                <TouchableOpacity
                                    style={styles.actionButton}
                                    onPress={handleHireBroker}
                                    activeOpacity={0.8}
                                >
                                    <LinearGradient
                                        colors={[
                                            theme.PRIMARY,
                                            theme.SECONDARY,
                                        ]}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1, y: 1 }}
                                        style={styles.gradient}
                                    />
                                    <MaterialIcons
                                        name="business"
                                        size={20}
                                        color={theme.WHITE}
                                    />
                                    <Text
                                        style={[
                                            styles.actionButtonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Hire Broker
                                    </Text>
                                </TouchableOpacity>
                            )}

                        {!mergedProject.contractorId && (
                            <TouchableOpacity
                                style={[
                                    styles.actionButton,
                                    mergedProject.brokerAssistanceRequired &&
                                    !mergedProject.brokerId &&
                                    styles.disabledButton,
                                ]}
                                onPress={handleHireContractor}
                                activeOpacity={0.8}
                                disabled={
                                    mergedProject.brokerAssistanceRequired &&
                                    !mergedProject.brokerId
                                }
                            >
                                <LinearGradient
                                    colors={
                                        mergedProject.brokerAssistanceRequired &&
                                            !mergedProject.brokerId
                                            ? [
                                                theme.TEXT_SECONDARY,
                                                theme.TEXT_SECONDARY,
                                            ]
                                            : [
                                                theme.SUCCESS,
                                                theme.SUCCESS + 'CC',
                                            ]
                                    }
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    style={styles.gradient}
                                />
                                <MaterialIcons
                                    name="build"
                                    size={20}
                                    color={theme.WHITE}
                                />
                                <Text
                                    style={[
                                        styles.actionButtonText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Hire Contractor
                                </Text>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        elevation: 2,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: '600',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 16,
    },
    editButton: {
        padding: 4,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        padding: 16,
        gap: 16,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        fontSize: 16,
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        gap: 16,
    },
    errorText: {
        fontSize: 18,
        fontWeight: '600',
    },
    section: {
        borderRadius: 12,
        padding: 16,
        elevation: 2,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
    },
    sectionContent: {
        gap: 12,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 8,
    },
    infoContent: {
        flex: 1,
    },
    infoLabel: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 2,
    },
    infoValue: {
        fontSize: 14,
        lineHeight: 20,
    },
    actionsContainer: {
        gap: 12,
        marginTop: 8,
    },
    actionButton: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 4,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 8,
    },
    disabledButton: {
        opacity: 0.6,
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    actionButtonText: {
        fontSize: 16,
        fontWeight: '600',
        paddingVertical: 16,
    },
});
