import React, { useState, useContext, useRef, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    TextInput,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    Switch,
    Alert,
    Dimensions,
    Animated,
    Easing,
    Image,
    ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { useMutation } from '@tanstack/react-query';
import { createProject } from '../../api/projects/projectApi.jsx';
import Toast from 'react-native-toast-message';

const { width, height } = Dimensions.get('window');

const validationSchema = Yup.object().shape({
    projectName: Yup.string().required('Project name is required'),
    projectType: Yup.string().required('Project type is required'),
    constructionType: Yup.string().required('Construction type is required'),
    address: Yup.string().required('Address is required'),
    city: Yup.string().required('City is required'),
    state: Yup.string().required('State is required'),
    pincode: Yup.string().required('Pincode is required'),
    plotSizeSqFt: Yup.number().positive('Plot size must be positive'),
    minBudget: Yup.number().positive('Minimum budget must be positive'),
    maxBudget: Yup.number()
        .positive('Maximum budget must be positive')
        .test(
            'max-greater-than-min',
            'Maximum budget must be greater than minimum budget',
            function (value) {
                const { minBudget } = this.parent;
                return !minBudget || !value || value > minBudget;
            }
        ),
    floors: Yup.number().positive('Number of floors must be positive'),
    bedrooms: Yup.number().min(0, 'Number of bedrooms cannot be negative'),
    bathrooms: Yup.number().min(0, 'Number of bathrooms cannot be negative'),
});

export default function CreateProject() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();

    // Animation refs
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    const createProjectMutation = useMutation({
        mutationFn: createProject,
        onSuccess: () => {
            Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Project created successfully!',
            });
            router.back();
        },
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to create project',
            });
        },
    });

    // Initialize animations
    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const projectTypes = ['Residential', 'Commercial', 'Industrial', 'Other'];
    const constructionTypes = ['New Construction', 'Renovation', 'Extension'];

    const initialValues = {
        projectName: '',
        projectType: '',
        constructionType: '',
        address: '',
        city: '',
        state: '',
        pincode: '',
        plotSizeSqFt: '',
        expectedStartDate: '',
        expectedCompletionDate: '',
        minBudget: '',
        maxBudget: '',
        floors: '',
        bedrooms: '',
        bathrooms: '',
        parkingRequired: false,
        gardenRequired: false,
        vastuCompliance: false,
        brokerAssistanceRequired: false,
        specialInstructions: '',
        additionalFacilities: '',
    };

    const handleSubmit = (values) => {
        const projectData = {
            projectName: values.projectName,
            projectType: values.projectType,
            constructionType: values.constructionType,
            location: {
                address: values.address,
                city: values.city,
                state: values.state,
                pincode: values.pincode,
                plotSizeSqFt: values.plotSizeSqFt
                    ? parseInt(values.plotSizeSqFt)
                    : undefined,
            },
            expectedStartDate: values.expectedStartDate || undefined,
            expectedCompletionDate: values.expectedCompletionDate || undefined,
            budget: {
                minBudget: values.minBudget
                    ? parseInt(values.minBudget)
                    : undefined,
                maxBudget: values.maxBudget
                    ? parseInt(values.maxBudget)
                    : undefined,
            },
            designPreferences: {
                floors: values.floors ? parseInt(values.floors) : undefined,
                bedrooms: values.bedrooms
                    ? parseInt(values.bedrooms)
                    : undefined,
                bathrooms: values.bathrooms
                    ? parseInt(values.bathrooms)
                    : undefined,
                parkingRequired: values.parkingRequired,
                gardenRequired: values.gardenRequired,
                vastuCompliance: values.vastuCompliance,
            },
            brokerAssistanceRequired: values.brokerAssistanceRequired,
            specialInstructions: values.specialInstructions || undefined,
            additionalFacilities: values.additionalFacilities
                ? values.additionalFacilities
                    .split(',')
                    .map((f) => f.trim())
                    .filter((f) => f)
                : [],
        };

        createProjectMutation.mutate(projectData);
    };

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Background Pattern */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            <SafeAreaView style={styles.safeArea}>
                {/* Header with Gradient */}
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    style={styles.header}
                >
                    <View style={styles.headerContent}>
                        <TouchableOpacity onPress={() => router.back()}>
                            <Ionicons name="arrow-back" size={24} color="#fff" />
                        </TouchableOpacity>
                        <Text style={styles.headerTitle}>Create Project</Text>
                        <TouchableOpacity>
                            <Ionicons name="person-circle" size={24} color="#fff" />
                        </TouchableOpacity>
                    </View>
                </LinearGradient>

                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                >
                    {({
                        values,
                        errors,
                        touched,
                        handleChange,
                        handleBlur,
                        handleSubmit,
                        setFieldValue,
                    }) => (
                        <ScrollView
                            style={styles.scrollView}
                            showsVerticalScrollIndicator={false}
                        >
                            <Animated.View
                                style={[
                                    styles.formContainer,
                                    {
                                        transform: [{ scale: scaleAnim }],
                                        opacity: fadeAnim,
                                        shadowColor: theme.SHADOW,
                                        backgroundColor: theme.CARD,
                                    },
                                ]}
                            >
                                {/* Project Name */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project Name *
                                    </Text>
                                    <View style={[
                                        styles.inputContainer,
                                        {
                                            borderColor: errors.projectName && touched.projectName
                                                ? theme.ERROR
                                                : theme.INPUT_BORDER,
                                            backgroundColor: theme.CARD,
                                        },
                                        errors.projectName && touched.projectName && styles.inputError
                                    ]}>
                                        <Ionicons
                                            name="home-outline"
                                            size={20}
                                            color={theme.TEXT_SECONDARY}
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                                            placeholder="Enter project name"
                                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                            value={values.projectName}
                                            onChangeText={handleChange('projectName')}
                                            onBlur={handleBlur('projectName')}
                                        />
                                    </View>
                                    {errors.projectName && touched.projectName && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.projectName}
                                        </Text>
                                    )}
                                </View>

                                {/* Project Type */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project Type *
                                    </Text>
                                    <View style={styles.optionsContainer}>
                                        {projectTypes.map((type) => (
                                            <TouchableOpacity
                                                key={type}
                                                style={[
                                                    styles.optionButton,
                                                    {
                                                        backgroundColor:
                                                            values.projectType ===
                                                                type
                                                                ? theme.PRIMARY
                                                                : theme.CARD,
                                                        borderColor:
                                                            values.projectType ===
                                                                type
                                                                ? theme.PRIMARY
                                                                : theme.BORDER,
                                                    },
                                                ]}
                                                onPress={() =>
                                                    setFieldValue(
                                                        'projectType',
                                                        type
                                                    )
                                                }
                                            >
                                                <Text
                                                    style={[
                                                        styles.optionText,
                                                        {
                                                            color:
                                                                values.projectType ===
                                                                    type
                                                                    ? theme.WHITE
                                                                    : theme.TEXT_PRIMARY,
                                                        },
                                                    ]}
                                                >
                                                    {type}
                                                </Text>
                                            </TouchableOpacity>
                                        ))}
                                    </View>
                                    {errors.projectType && touched.projectType && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.projectType}
                                        </Text>
                                    )}
                                </View>

                                {/* Construction Type */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Construction Type *
                                    </Text>
                                    <View style={styles.optionsContainer}>
                                        {constructionTypes.map((type) => (
                                            <TouchableOpacity
                                                key={type}
                                                style={[
                                                    styles.optionButton,
                                                    {
                                                        backgroundColor:
                                                            values.constructionType ===
                                                                type
                                                                ? theme.PRIMARY
                                                                : theme.CARD,
                                                        borderColor:
                                                            values.constructionType ===
                                                                type
                                                                ? theme.PRIMARY
                                                                : theme.BORDER,
                                                    },
                                                ]}
                                                onPress={() =>
                                                    setFieldValue(
                                                        'constructionType',
                                                        type
                                                    )
                                                }
                                            >
                                                <Text
                                                    style={[
                                                        styles.optionText,
                                                        {
                                                            color:
                                                                values.constructionType ===
                                                                    type
                                                                    ? theme.WHITE
                                                                    : theme.TEXT_PRIMARY,
                                                        },
                                                    ]}
                                                >
                                                    {type}
                                                </Text>
                                            </TouchableOpacity>
                                        ))}
                                    </View>
                                    {errors.constructionType &&
                                        touched.constructionType && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.constructionType}
                                            </Text>
                                        )}
                                </View>

                                {/* Location Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="location-on"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Location Details
                                    </Text>
                                </View>

                                {/* Address */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Address *
                                    </Text>
                                    <TextInput
                                        style={[
                                            styles.input,
                                            {
                                                backgroundColor: theme.CARD,
                                                color: theme.TEXT_PRIMARY,
                                                borderColor:
                                                    errors.address &&
                                                        touched.address
                                                        ? theme.ERROR
                                                        : theme.BORDER,
                                            },
                                        ]}
                                        placeholder="Enter full address"
                                        placeholderTextColor={theme.TEXT_SECONDARY}
                                        value={values.address}
                                        onChangeText={handleChange('address')}
                                        onBlur={handleBlur('address')}
                                        multiline
                                    />
                                    {errors.address && touched.address && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.address}
                                        </Text>
                                    )}
                                </View>

                                {/* City and State Row */}
                                <View style={styles.row}>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            City *
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.city && touched.city
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="City"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.city}
                                            onChangeText={handleChange('city')}
                                            onBlur={handleBlur('city')}
                                        />
                                        {errors.city && touched.city && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.city}
                                            </Text>
                                        )}
                                    </View>

                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            State *
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.state &&
                                                            touched.state
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="State"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.state}
                                            onChangeText={handleChange('state')}
                                            onBlur={handleBlur('state')}
                                        />
                                        {errors.state && touched.state && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.state}
                                            </Text>
                                        )}
                                    </View>
                                </View>

                                {/* Pincode and Plot Size Row */}
                                <View style={styles.row}>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Pincode *
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.pincode &&
                                                            touched.pincode
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="Pincode"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.pincode}
                                            onChangeText={handleChange('pincode')}
                                            onBlur={handleBlur('pincode')}
                                            keyboardType="numeric"
                                        />
                                        {errors.pincode && touched.pincode && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.pincode}
                                            </Text>
                                        )}
                                    </View>

                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Plot Size (Sq Ft)
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.plotSizeSqFt &&
                                                            touched.plotSizeSqFt
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="Plot Size"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.plotSizeSqFt}
                                            onChangeText={handleChange(
                                                'plotSizeSqFt'
                                            )}
                                            onBlur={handleBlur('plotSizeSqFt')}
                                            keyboardType="numeric"
                                        />
                                        {errors.plotSizeSqFt &&
                                            touched.plotSizeSqFt && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.plotSizeSqFt}
                                                </Text>
                                            )}
                                    </View>
                                </View>

                                {/* Budget Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="attach-money"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Budget Details
                                    </Text>
                                </View>

                                {/* Budget Row */}
                                <View style={styles.row}>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Min Budget (₹)
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.minBudget &&
                                                            touched.minBudget
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="Min Budget"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.minBudget}
                                            onChangeText={handleChange('minBudget')}
                                            onBlur={handleBlur('minBudget')}
                                            keyboardType="numeric"
                                        />
                                        {errors.minBudget && touched.minBudget && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.minBudget}
                                            </Text>
                                        )}
                                    </View>

                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Max Budget (₹)
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.maxBudget &&
                                                            touched.maxBudget
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="Max Budget"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.maxBudget}
                                            onChangeText={handleChange('maxBudget')}
                                            onBlur={handleBlur('maxBudget')}
                                            keyboardType="numeric"
                                        />
                                        {errors.maxBudget && touched.maxBudget && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.maxBudget}
                                            </Text>
                                        )}
                                    </View>
                                </View>

                                {/* Design Preferences Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="home"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Design Preferences
                                    </Text>
                                </View>

                                {/* Floors, Bedrooms, Bathrooms Row */}
                                <View style={styles.row}>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Floors
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.floors &&
                                                            touched.floors
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="Floors"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.floors}
                                            onChangeText={handleChange('floors')}
                                            onBlur={handleBlur('floors')}
                                            keyboardType="numeric"
                                        />
                                        {errors.floors && touched.floors && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.floors}
                                            </Text>
                                        )}
                                    </View>

                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginHorizontal: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Bedrooms
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.bedrooms &&
                                                            touched.bedrooms
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="Bedrooms"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.bedrooms}
                                            onChangeText={handleChange('bedrooms')}
                                            onBlur={handleBlur('bedrooms')}
                                            keyboardType="numeric"
                                        />
                                        {errors.bedrooms && touched.bedrooms && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.bedrooms}
                                            </Text>
                                        )}
                                    </View>

                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Bathrooms
                                        </Text>
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    color: theme.TEXT_PRIMARY,
                                                    borderColor:
                                                        errors.bathrooms &&
                                                            touched.bathrooms
                                                            ? theme.ERROR
                                                            : theme.BORDER,
                                                },
                                            ]}
                                            placeholder="Bathrooms"
                                            placeholderTextColor={
                                                theme.TEXT_SECONDARY
                                            }
                                            value={values.bathrooms}
                                            onChangeText={handleChange('bathrooms')}
                                            onBlur={handleBlur('bathrooms')}
                                            keyboardType="numeric"
                                        />
                                        {errors.bathrooms && touched.bathrooms && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.bathrooms}
                                            </Text>
                                        )}
                                    </View>
                                </View>







                                {/* Additional Details Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="description"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Additional Details
                                    </Text>
                                </View>

                                {/* Additional Facilities */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Additional Facilities (comma separated)
                                    </Text>
                                    <TextInput
                                        style={[
                                            styles.input,
                                            styles.textArea,
                                            {
                                                backgroundColor: theme.CARD,
                                                color: theme.TEXT_PRIMARY,
                                                borderColor: theme.BORDER,
                                            },
                                        ]}
                                        placeholder="e.g., Swimming pool, Gym, Balcony"
                                        placeholderTextColor={theme.TEXT_SECONDARY}
                                        value={values.additionalFacilities}
                                        onChangeText={handleChange(
                                            'additionalFacilities'
                                        )}
                                        onBlur={handleBlur('additionalFacilities')}
                                        multiline
                                        numberOfLines={3}
                                    />
                                </View>

                                {/* Special Instructions */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Special Instructions
                                    </Text>
                                    <TextInput
                                        style={[
                                            styles.input,
                                            styles.textArea,
                                            {
                                                backgroundColor: theme.CARD,
                                                color: theme.TEXT_PRIMARY,
                                                borderColor: theme.BORDER,
                                            },
                                        ]}
                                        placeholder="Any special requirements or instructions..."
                                        placeholderTextColor={theme.TEXT_SECONDARY}
                                        value={values.specialInstructions}
                                        onChangeText={handleChange(
                                            'specialInstructions'
                                        )}
                                        onBlur={handleBlur('specialInstructions')}
                                        multiline
                                        numberOfLines={4}
                                    />
                                </View>

                                {/* Submit Button */}
                                <TouchableOpacity
                                    style={[
                                        styles.submitButton,
                                        {
                                            borderColor: theme.PRIMARY,
                                            opacity: createProjectMutation.isPending ? 0.7 : 1,
                                        },
                                    ]}
                                    onPress={handleSubmit}
                                    disabled={createProjectMutation.isPending}
                                    activeOpacity={0.8}
                                >
                                    <LinearGradient
                                        colors={[theme.PRIMARY, theme.SECONDARY]}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1, y: 0 }}
                                        style={styles.submitButtonGradient}
                                    >
                                        <Text
                                            style={[
                                                styles.submitText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            {createProjectMutation.isPending
                                                ? 'Creating...'
                                                : 'Create Project'}
                                        </Text>
                                    </LinearGradient>
                                </TouchableOpacity>
                            </Animated.View>
                        </ScrollView>
                    )}
                </Formik>

                {/* Loading Overlay */}
                {createProjectMutation.isPending && (
                    <View style={styles.scanningOverlay}>
                        <View style={styles.scanningContainer}>
                            <ActivityIndicator size="large" color={theme.PRIMARY} />
                            <Text style={[styles.scanningText, { color: theme.TEXT_PRIMARY }]}>
                                Creating Project...
                            </Text>
                        </View>
                    </View>
                )}
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    safeArea: {
        flex: 1,
    },
    header: {
        paddingTop: 20,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    scrollView: {
        flex: 1,
    },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        margin: 20,
        alignSelf: 'center',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    inputGroup: {
        marginBottom: 20,
    },
    label: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 12,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 8,
        paddingHorizontal: 12,
        height: 56,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 16,
    },
    inputError: {
        borderColor: 'red',
    },
    optionsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    optionButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        marginRight: 8,
        marginBottom: 8,
    },
    optionText: {
        fontSize: 14,
        fontWeight: '500',
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 24,
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
    },
    row: {
        flexDirection: 'row',
    },
    errorText: {
        fontSize: 12,
        marginTop: 4,
    },
    submitButton: {
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        marginTop: 32,
        marginBottom: 8,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 14,
        alignItems: 'center',
    },
    submitText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    switchContainer: {
        marginTop: 16,
    },
    switchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
    },
    switchLabel: {
        fontSize: 14,
        fontWeight: '500',
        flex: 1,
    },
    textArea: {
        minHeight: 80,
        textAlignVertical: 'top',
        paddingTop: 12,
    },
    scanningOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    scanningContainer: {
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 30,
        alignItems: 'center',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 10,
        elevation: 10,
    },
    scanningText: {
        fontSize: 16,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
});
